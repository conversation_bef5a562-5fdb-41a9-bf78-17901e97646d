import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/member_type.dart';

part 'member_type_model.freezed.dart';
part 'member_type_model.g.dart';

@freezed
abstract class MemberTypeModel with _$MemberTypeModel {
  const factory MemberTypeModel({
    required int id,
    @JsonKey(name: 'name_th') required String nameTh,
    @JsonKey(name: 'name_en') required String nameEn,
  }) = _MemberTypeModel;

  factory MemberTypeModel.fromJson(Map<String, dynamic> json) =>
      _$MemberTypeModelFromJson(json);
}

/// Extension to convert model to entity
extension MemberTypeModelX on MemberTypeModel {
  MemberType toEntity() {
    return MemberType(
      id: id,
      nameTh: nameTh,
      nameEn: nameEn,
    );
  }
}
