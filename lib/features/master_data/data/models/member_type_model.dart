import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/member_type.dart';

part 'member_type_model.freezed.dart';
part 'member_type_model.g.dart';

@freezed
class MemberTypeModel with _$MemberTypeModel {
  const factory MemberTypeModel({
    required int id,
    required String nameTh,
    required String nameEn,
  }) = _MemberTypeModel;

  factory MemberTypeModel.fromJson(Map<String, dynamic> json) =>
      _$MemberTypeModelFromJson(json);

  // Custom fromJson to handle API field names
  factory MemberTypeModel.fromApiJson(Map<String, dynamic> json) {
    return MemberTypeModel(
      id: json['id'] as int,
      nameTh: json['name_th'] as String,
      nameEn: json['name_en'] as String,
    );
  }
}

/// Extension to convert model to entity
extension MemberTypeModelX on MemberTypeModel {
  MemberType toEntity() {
    return MemberType(id: id, nameTh: nameTh, nameEn: nameEn);
  }
}
