import 'package:mcdc/core/api/api_client.dart';
import 'package:mcdc/core/api/api_endpoints.dart';
import 'package:mcdc/core/api/model/result.dart';
import 'package:mcdc/core/api/model/api_response_parser.dart';
import 'package:mcdc/core/error/exceptions.dart';
import '../models/member_type_model.dart';

abstract class MasterDataApiDataSource {
  Future<Result<List<MemberTypeModel>>> getMemberTypes();
}

class MasterDataApiDataSourceImpl implements MasterDataApiDataSource {
  final ApiClient _apiClient;

  const MasterDataApiDataSourceImpl(this._apiClient);

  @override
  Future<Result<List<MemberTypeModel>>> getMemberTypes() async {
    final result = await _apiClient.get<dynamic>(
      ApiEndpoints.memberTypes,
      queryParameters: {'page_size': 999},
    );

    // Use the new Result-based approach with flexible parsing
    return result.map<List<MemberTypeModel>>((responseData) {
      if (responseData is Map<String, dynamic>) {
        // Try to parse using the flexible response parser
        final parseResult = ApiResponseParser.parseFlexibleResponse<
          List<MemberTypeModel>
        >(responseData, (data) {
          if (data is List) {
            final List<MemberTypeModel> memberTypes = [];
            for (final item in data) {
              if (item is Map<String, dynamic>) {
                memberTypes.add(MemberTypeModel.fromApiJson(item));
              }
            }
            return memberTypes;
          } else {
            throw ServerException(message: 'Expected list of member types');
          }
        });

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          throw ServerException(
            code: parseResult.errorCode,
            message: parseResult.errorMessage,
          );
        }
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }
}
