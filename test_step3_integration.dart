import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mcdc/features/user/domain/entities/sector_type.dart';
import 'package:mcdc/features/user/domain/entities/organization.dart';
import 'package:mcdc/features/user/domain/entities/additional_data.dart';
import 'package:mcdc/features/user/presentation/bloc/additional_data_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

void main() {
  group('Step 3 Additional Data Tests', () {
    late AdditionalDataCubit cubit;

    setUp(() {
      cubit = AdditionalDataCubit();
    });

    tearDown(() {
      cubit.close();
    });

    test('should initialize with empty data', () {
      expect(cubit.firstName, isEmpty);
      expect(cubit.lastName, isEmpty);
      expect(cubit.selectedSectorType, isNull);
      expect(cubit.departmentName, isEmpty);
    });

    test('should update first name', () {
      const testName = 'John';
      cubit.updateFirstName(testName);
      expect(cubit.firstName, equals(testName));
    });

    test('should update last name', () {
      const testName = 'Doe';
      cubit.updateLastName(testName);
      expect(cubit.lastName, equals(testName));
    });

    test('should update sector type', () {
      cubit.updateSectorType(SectorType.government);
      expect(cubit.selectedSectorType, equals(SectorType.government));
    });

    test('should update department name', () {
      const testDepartment = 'IT Department';
      cubit.updateDepartmentName(testDepartment);
      expect(cubit.departmentName, equals(testDepartment));
    });

    test('should provide sector types', () {
      final sectorTypes = cubit.sectorTypes;
      expect(sectorTypes, isNotEmpty);
      expect(sectorTypes, contains(SectorType.government));
      expect(sectorTypes, contains(SectorType.private));
    });

    test('should create additional data when all required fields are filled', () {
      cubit.updateFirstName('John');
      cubit.updateLastName('Doe');
      cubit.updateSectorType(SectorType.private);
      cubit.updateDepartmentName('IT Department');

      final additionalData = cubit.getAdditionalData();
      expect(additionalData, isNotNull);
      expect(additionalData!.firstName, equals('John'));
      expect(additionalData.lastName, equals('Doe'));
      expect(additionalData.sectorType, equals(SectorType.private));
      expect(additionalData.departmentName, equals('IT Department'));
    });

    test('should return null when required fields are missing', () {
      cubit.updateFirstName('John');
      // Missing last name, sector type, and department name

      final additionalData = cubit.getAdditionalData();
      expect(additionalData, isNull);
    });
  });

  group('SectorType Tests', () {
    test('should have correct government sector type', () {
      expect(SectorType.government.id, equals(1));
      expect(SectorType.government.name, equals('government'));
      expect(SectorType.government.displayName, equals('ภาครัฐ'));
    });

    test('should have correct private sector type', () {
      expect(SectorType.private.id, equals(2));
      expect(SectorType.private.name, equals('private'));
      expect(SectorType.private.displayName, equals('ภาคเอกชน'));
    });

    test('should provide all sector types', () {
      final all = SectorType.all;
      expect(all.length, equals(2));
      expect(all, contains(SectorType.government));
      expect(all, contains(SectorType.private));
    });
  });

  group('Organization Tests', () {
    test('should create organization with required fields', () {
      const org = Organization(
        id: 1,
        name: 'test_org',
        displayName: 'Test Organization',
      );

      expect(org.id, equals(1));
      expect(org.name, equals('test_org'));
      expect(org.displayName, equals('Test Organization'));
      expect(org.parentId, isNull);
    });

    test('should create organization with parent', () {
      const org = Organization(
        id: 2,
        name: 'child_org',
        displayName: 'Child Organization',
        parentId: 1,
      );

      expect(org.parentId, equals(1));
    });
  });

  group('AdditionalData Tests', () {
    test('should be valid when all required fields are provided for private sector', () {
      const data = AdditionalData(
        firstName: 'John',
        lastName: 'Doe',
        sectorType: SectorType.private,
        departmentName: 'IT Department',
      );

      expect(data.isValid, isTrue);
    });

    test('should be valid when all required fields are provided for government sector', () {
      const governmentAgency = Organization(
        id: 1,
        name: 'central_gov',
        displayName: 'Central Government',
      );

      const data = AdditionalData(
        firstName: 'Jane',
        lastName: 'Smith',
        sectorType: SectorType.government,
        governmentAgency: governmentAgency,
        departmentName: 'HR Department',
      );

      expect(data.isValid, isTrue);
    });

    test('should be invalid when government sector is selected but no agency is provided', () {
      const data = AdditionalData(
        firstName: 'Jane',
        lastName: 'Smith',
        sectorType: SectorType.government,
        departmentName: 'HR Department',
      );

      expect(data.isValid, isFalse);
    });

    test('should be invalid when required fields are empty', () {
      const data = AdditionalData(
        firstName: '',
        lastName: 'Smith',
        sectorType: SectorType.private,
        departmentName: 'HR Department',
      );

      expect(data.isValid, isFalse);
    });
  });
}
